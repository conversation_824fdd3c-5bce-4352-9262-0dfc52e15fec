using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Esky.Hangfire.Metrics;

namespace Esky.Hangfire.Sample.API
{
    public static class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .UseStartup<Startup>()
                .ConfigureHangfirePrometheusMetrics();
    }
}
