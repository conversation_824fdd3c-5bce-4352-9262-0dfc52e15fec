using Esky.Hangfire.CustomJobNames.Tags;
using Hangfire.Common;
using System.Linq;
using System.Reflection;
using Xunit;

namespace Esky.Hangfire.Tests
{
    public class CustomJobTagAttributeTests
    {
        [Fact]
        public void ConstJobTag_ShouldReturnSpecifiedTagValue()
        {
            var method = typeof(JobMock).GetMethod("Execute");
            var job = new Job(method);
            var attribute = job.Method.GetCustomAttributes<JobTagAttribute>().Single();

            Assert.Equal("Custom1", attribute.TagName);
            Assert.Equal("test", attribute.GetTagValue(job));
        }

        public class JobMock
        {
            [ConstJobTag("Custom1", "test")]
            public void Execute()
            {
            }
        }
    }
}
