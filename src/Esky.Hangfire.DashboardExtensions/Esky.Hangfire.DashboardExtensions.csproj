<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <VersionPrefix>2.0</VersionPrefix>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <TargetFramework>net9.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Core" Version="1.8.21" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\JobsFilteredPage.generated.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>JobsFilteredPage.cshtml</DependentUpon>
    </Compile>
    <Compile Update="Pages\JobsGroupedPage.generated.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>JobsGroupedPage.cshtml</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\JobsFilteredPage.cshtml">
      <Generator>RazorGenerator</Generator>
      <LastGenOutput>JobsFilteredPage.generated.cs</LastGenOutput>
      <CustomToolNamespace>HangfireExtended.Dashboard</CustomToolNamespace>
    </None>
    <None Update="Pages\JobsGroupedPage.cshtml">
      <LastGenOutput>JobsGroupedPage.generated.cs</LastGenOutput>
      <Generator>RazorGenerator</Generator>
      <CustomToolNamespace>HangfireExtended.Dashboard</CustomToolNamespace>
    </None>
  </ItemGroup>
  
</Project>
