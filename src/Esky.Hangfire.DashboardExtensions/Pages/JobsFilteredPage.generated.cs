#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HangfireExtended.Dashboard
{
    using System;
    using System.Collections.Generic;
    
    #line 2 "..\..\Pages\JobsFilteredPage.cshtml"
    using System.Linq;
    
    #line default
    #line hidden
    using System.Text;
    
    #line 5 "..\..\Pages\JobsFilteredPage.cshtml"
    using Esky.Hangfire.DashboardExtensions;
    
    #line default
    #line hidden
    
    #line 3 "..\..\Pages\JobsFilteredPage.cshtml"
    using Hangfire.Dashboard.Pages;
    
    #line default
    #line hidden
    
    #line 4 "..\..\Pages\JobsFilteredPage.cshtml"
    using Hangfire.Dashboard.Resources;
    
    #line default
    #line hidden
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("RazorGenerator", "2.0.0.0")]
    internal partial class JobsFilteredPage : global::Hangfire.Dashboard.RazorPage
    {
#line hidden

        public override void Execute()
        {


WriteLiteral("\r\n");





WriteLiteral("\r\n");



            
            #line 8 "..\..\Pages\JobsFilteredPage.cshtml"
  
    Layout = new LayoutPage("Jobs filtered");
    var sw = System.Diagnostics.Stopwatch.StartNew();

    var jobFilter = this.CreateJobFilterFromQueryString();

    var jobs = this.extendedMonitoringApi.GetJobs(jobFilter);

    sw.Stop();


    Func<string, string> stateNameToClass = JobStatuses.GetCssClass;


            
            #line default
            #line hidden
WriteLiteral("\r\n\r\n<div class=\"row\">\r\n    <div class=\"col-md-3\">\r\n        ");


            
            #line 36 "..\..\Pages\JobsFilteredPage.cshtml"
   Write(Html.JobsSidebar());

            
            #line default
            #line hidden
WriteLiteral(@"
    </div>
    <div class=""col-md-9"">
        <h1 class=""page-header"">Jobs filtered</h1>



        <div class=""js-jobs-list"">



            <div class=""btn-toolbar btn-toolbar-top"">


                <form method=""get"" class=""form-inline"">
                    <div class=""form-group"">
                        From:
                        <input class=""form-control"" type=""datetime"" value=""");


            
            #line 53 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                      Write(jobFilter.DateFrom.ToString("yyyy-MM-dd HH:mm:ss"));

            
            #line default
            #line hidden
WriteLiteral("\" name=\"dateFrom\" />\r\n                        To:\r\n                        <input" +
" class=\"form-control\" type=\"datetime\" value=\"");


            
            #line 55 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                      Write(jobFilter.DateTo.ToString("yyyy-MM-dd HH:mm:ss"));

            
            #line default
            #line hidden
WriteLiteral(@""" name=""dateTo"" />

                        Quick time range - last
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'h').format('YYYY-MM-DD HH:mm:00')); return false;"">1 hour</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-4, 'h').format('YYYY-MM-DD HH:mm:00')); return false;"">4 hours</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-1, 'd').format('YYYY-MM-DD HH:mm:00')); return false;"">1 day</a> |
                        <a href=""#"" onclick=""$('input[name=dateTo]').val(moment().utc().format('YYYY-MM-DD HH:mm:00')); $('input[name=dateFrom]').val(moment().utc().add(-7, 'd').format('YYYY-MM-DD HH:mm:00')); return false;"">7 days</a>

                    </div>
                    <div class=""form-group"">
                        Search:
                        <input class=""form-control"" type=""text"" value=""");


            
            #line 66 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                  Write(jobFilter.SearchPhrase);

            
            #line default
            #line hidden
WriteLiteral("\" name=\"searchPhrase\" />\r\n                        State:\r\n                       " +
" <select class=\"form-control\" name=\"stateName\">\r\n");


            
            #line 69 "..\..\Pages\JobsFilteredPage.cshtml"
                             foreach (var option in JobStatuses.FilterableStatuses)
                            {

            
            #line default
            #line hidden
WriteLiteral("                                <option value=\"");


            
            #line 71 "..\..\Pages\JobsFilteredPage.cshtml"
                                          Write(option);

            
            #line default
            #line hidden
WriteLiteral("\" ");


            
            #line 71 "..\..\Pages\JobsFilteredPage.cshtml"
                                                    Write(option == jobFilter.StateName ? "selected=\"selected\"" : "");

            
            #line default
            #line hidden
WriteLiteral(">");


            
            #line 71 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                                                                   Write(option);

            
            #line default
            #line hidden
WriteLiteral("</option>\r\n");


            
            #line 72 "..\..\Pages\JobsFilteredPage.cshtml"
                            }

            
            #line default
            #line hidden
WriteLiteral(@"                        </select>
                    </div>

                    <div class=""form-group"">
                        <input type=""submit"" value=""Filter"" class=""btn btn-default"" />
                    </div>
                </form>
                <br />
                <div>
                    <button class=""js-jobs-list-command btn btn-sm btn-primary""
                            data-url=""");


            
            #line 83 "..\..\Pages\JobsFilteredPage.cshtml"
                                 Write(Url.To("/jobs/failed/requeue"));

            
            #line default
            #line hidden
WriteLiteral("\"\r\n                            data-loading-text=\"");


            
            #line 84 "..\..\Pages\JobsFilteredPage.cshtml"
                                          Write(Strings.Common_Enqueueing);

            
            #line default
            #line hidden
WriteLiteral("\"\r\n                            disabled=\"disabled\">\r\n                        <spa" +
"n class=\"glyphicon glyphicon-repeat\"></span>\r\n                        ");


            
            #line 87 "..\..\Pages\JobsFilteredPage.cshtml"
                   Write(Strings.Common_RequeueJobs);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </button>\r\n                    <button class=\"js-jobs-list-" +
"command btn btn-sm btn-default\"\r\n                            data-url=\"");


            
            #line 90 "..\..\Pages\JobsFilteredPage.cshtml"
                                 Write(Url.To("/jobs/failed/delete"));

            
            #line default
            #line hidden
WriteLiteral("\"\r\n                            data-loading-text=\"");


            
            #line 91 "..\..\Pages\JobsFilteredPage.cshtml"
                                          Write(Strings.Common_Deleting);

            
            #line default
            #line hidden
WriteLiteral("\"\r\n                            data-confirm=\"");


            
            #line 92 "..\..\Pages\JobsFilteredPage.cshtml"
                                     Write(Strings.Common_DeleteConfirm);

            
            #line default
            #line hidden
WriteLiteral("\">\r\n                        <span class=\"glyphicon glyphicon-remove\"></span>\r\n   " +
"                     ");


            
            #line 94 "..\..\Pages\JobsFilteredPage.cshtml"
                   Write(Strings.Common_DeleteSelected);

            
            #line default
            #line hidden
WriteLiteral(@"
                    </button>
                </div>
            </div>

            <div class=""table-responsive"">

                <table class=""table"">
                    <thead>

                        <tr>
                            <th class=""min-width"">
                                <input type=""checkbox"" class=""js-jobs-list-select-all"" />
                            </th>
                            <th class=""min-width"">");


            
            #line 108 "..\..\Pages\JobsFilteredPage.cshtml"
                                             Write(Strings.Common_Id);

            
            #line default
            #line hidden
WriteLiteral("</th>\r\n                            <th colspan=\"3\">");


            
            #line 109 "..\..\Pages\JobsFilteredPage.cshtml"
                                       Write(Strings.Common_Job);

            
            #line default
            #line hidden
WriteLiteral("</th>\r\n                        </tr>\r\n                    </thead>\r\n             " +
"       <tbody>\r\n");


            
            #line 113 "..\..\Pages\JobsFilteredPage.cshtml"
                         foreach (var job in jobs)
                        {

            
            #line default
            #line hidden
WriteLiteral("                            <tr class=\"js-jobs-list-row\">\r\n                      " +
"          <td colspan=\"2\">\r\n                                    <input type=\"che" +
"ckbox\" class=\"js-jobs-list-checkbox\" name=\"jobs[]\" value=\"");


            
            #line 117 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                                                         Write(job.Id);

            
            #line default
            #line hidden
WriteLiteral("\" />\r\n                                </td>\r\n                                <td>" +
"");


            
            #line 119 "..\..\Pages\JobsFilteredPage.cshtml"
                               Write(Html.JobIdLink(job.Id));

            
            #line default
            #line hidden
WriteLiteral("</td>\r\n                                <td>\r\n                                    " +
"<div>\r\n                                        <strong>");


            
            #line 122 "..\..\Pages\JobsFilteredPage.cshtml"
                                           Write(job.Method);

            
            #line default
            #line hidden
WriteLiteral("</strong>\r\n                                        <small>");


            
            #line 123 "..\..\Pages\JobsFilteredPage.cshtml"
                                          Write(job.Type);

            
            #line default
            #line hidden
WriteLiteral("</small>\r\n                                    </div>\r\n");


            
            #line 125 "..\..\Pages\JobsFilteredPage.cshtml"
                                      
                                        var arr = Newtonsoft.Json.Linq.JArray.Parse(job.Arguments);
                                        var argsText = string.Join(" | ", arr.Values<string>());
                                    

            
            #line default
            #line hidden
WriteLiteral("                                    <textarea disabled style=\"width: 350px\">");


            
            #line 129 "..\..\Pages\JobsFilteredPage.cshtml"
                                                                       Write(argsText);

            
            #line default
            #line hidden
WriteLiteral("</textarea>                                    \r\n                                " +
"</td>\r\n                                <td>\r\n                                   " +
" <span class=\"metric ");


            
            #line 132 "..\..\Pages\JobsFilteredPage.cshtml"
                                                   Write(stateNameToClass(job.StateName));

            
            #line default
            #line hidden
WriteLiteral("\">\r\n                                        ");


            
            #line 133 "..\..\Pages\JobsFilteredPage.cshtml"
                                   Write(job.StateName);

            
            #line default
            #line hidden
WriteLiteral("\r\n                                    </span>\r\n                                </" +
"td>\r\n                                <td>\r\n                                    ");


            
            #line 137 "..\..\Pages\JobsFilteredPage.cshtml"
                               Write(Html.RelativeTime(job.CreatedAt));

            
            #line default
            #line hidden
WriteLiteral("\r\n                                </td>\r\n                            </tr>\r\n");


            
            #line 140 "..\..\Pages\JobsFilteredPage.cshtml"

                        }

            
            #line default
            #line hidden
WriteLiteral("                    </tbody>\r\n\r\n\r\n                </table>\r\n\r\n            </div>\r" +
"\n\r\n        </div>\r\n\r\n\r\n        <div>\r\n            <h4>Query:</h4>\r\n\r\n           " +
" <small class=\"text-muted\">\r\n                ");


            
            #line 156 "..\..\Pages\JobsFilteredPage.cshtml"
           Write(this.extendedMonitoringApi.GetJobFilterAsString(jobFilter));

            
            #line default
            #line hidden
WriteLiteral("\r\n            </small>\r\n            <br />\r\n\r\n            Elapsed: ");


            
            #line 160 "..\..\Pages\JobsFilteredPage.cshtml"
                Write(sw.Elapsed.TotalSeconds.ToString("0.#"));

            
            #line default
            #line hidden
WriteLiteral(" s\r\n        </div>\r\n    </div>\r\n</div>\r\n");


        }
    }
}
#pragma warning restore 1591
