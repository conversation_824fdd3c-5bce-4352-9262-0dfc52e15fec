using Hangfire.States;
using System.Collections.Generic;
using System.Linq;

namespace Esky.Hangfire.DashboardExtensions
{
    public static class JobStatuses
    {
        public static readonly string Enqueued = EnqueuedState.StateName;
        public static readonly string Scheduled = ScheduledState.StateName;
        public static readonly string Processing = ProcessingState.StateName;
        public static readonly string Succeeded = SucceededState.StateName;
        public static readonly string Failed = FailedState.StateName;
        public static readonly string Deleted = DeletedState.StateName;

        public static readonly IReadOnlyList<string> AllStatuses = new[]
        {
            Enqueued,
            Scheduled,
            Processing,
            Succeeded,
            Failed,
            Deleted
        };

        public static readonly IReadOnlyList<string> FilterableStatuses = new[]
        {
            "",
            Succeeded,
            Processing,
            Failed,
            Deleted,
            Scheduled
        };

        public static string GetCssClass(string stateName)
        {
            return stateName switch
            {
                var s when s == Enqueued => "metric-info highlighted",
                var s when s == Scheduled => "metric-info highlighted",
                var s when s == Processing => "metric-warning",
                var s when s == Succeeded => "metric-default",
                var s when s == Failed => "metric-danger highlighted",
                _ => "metric-default"
            };
        }
    }
}
