<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
	<VersionPrefix>2.0</VersionPrefix>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="App.Metrics.AspNetCore" Version="4.3.0" />
    <PackageReference Include="App.Metrics.Formatters.Prometheus" Version="4.3.0" />
    <PackageReference Include="Hangfire" Version="1.8.21" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Esky.Hangfire.CustomJobNames\Esky.Hangfire.CustomJobNames.csproj" />
  </ItemGroup>
</Project>