name: MASTER-NUGET-BUILD-PUBLISH
run-name: BUILD & PUBLISH master nugets by @${{ github.actor }}
on:
  push:
    branches:
       - master
  workflow_dispatch:

permissions:
      contents: read
      packages: write

jobs:
  build-and-publish-nugets:
    name: Build and publish nugets
    runs-on: k8s-runner
    env:
        DOTNET_INSTALL_DIR: ./.dotnet
        DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
        DOTNET_NOLOGO: true
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup dotnet 9
        uses: actions/setup-dotnet@v3
        with:
         dotnet-version: 9.0.x

      - name: Restore dependencies
        run: dotnet restore src/Esky.Hangfire.sln

      - name: Build solution
        run: dotnet build src/Esky.Hangfire.sln --configuration Release --no-restore

      - name: Push generated NuGet packages
        run: dotnet nuget push "**/bin/Release/*.nupkg" --api-key ${{ secrets.ARTIFACTORY_NUGET_TOKEN  }} --source "https://artifactory.eskyspace.com/api/nuget/nuget-local/" --skip-duplicate
