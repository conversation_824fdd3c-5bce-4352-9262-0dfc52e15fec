name: PR-BUILD
run-name: BUILD PR by @${{ github.actor }} on ${{ github.head_ref }}
on:
  pull_request:
  workflow_dispatch:
    inputs:
      run_all_tests:
        description: Force to run all tests
        required: false
        type: boolean
        default: false

permissions:
  checks: write
  contents: write
  pull-requests: write

jobs:
  run-tests:
    name: Run Tests
    runs-on: k8s-runner
    env:
      DOTNET_INSTALL_DIR: ./.dotnet

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup dotnet
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 9.0.x

      - name: Check affected projects
        if: inputs.run_all_tests != true
        uses: leonardochaia/dotnet-affected-action@v1
        id: dotnet_affected
        with:
          from: remotes/origin/${{ github.head_ref }}
          to: remotes/origin/${{ github.base_ref }}

      - name: Print affected projects
        if: success() && steps.dotnet_affected.outputs.affected != ''
        run: |
          echo "Checking for changes in projects"
          echo "Affected projects: ${{steps.dotnet_affected.outputs.affected}}"

      - name: Test affected projects
        if: success() && steps.dotnet_affected.outputs.affected != ''
        run: dotnet test --verbosity normal affected.proj --logger "trx;LogFileName=test-results.trx" --filter "Category!=IntegrationTests"

      - name: Test all (forced)
        if: success() && inputs.run_all_tests == true
        run: dotnet test ./src/Esky.Hangfire.sln --verbosity normal --logger "trx;LogFileName=test-results.trx" --filter "Category!=IntegrationTests"

      - name: Generate test report
        if: success() || failure()
        uses: dorny/test-reporter@v1
        with:
          name: DotNET Tests
          path: "**/test-results.trx"
          reporter: dotnet-trx
          fail-on-error: true
          fail-on-empty: false
          list-tests: failed    # Because the result exceeds limit, list only failed tests (count is not affected)
          max-annotations: 10   # Ditto
